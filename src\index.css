@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 217 91% 60%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 47% 11%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 212 27% 84%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 224 76% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: #000000;
    /* Improve touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    /* Prevent zoom on input focus on iOS */
    -webkit-text-size-adjust: 100%;
  }

  body {
    @apply bg-black text-foreground tracking-tight antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02";
    font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI",
      Roboto, Oxygen, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    /* Improve text rendering on mobile */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Fallback to system fonts if SF Pro Display is not available */
  @font-face {
    font-family: "SF Pro Display";
    src: local("SF Pro Display"), local("SFProDisplay-Regular"),
      local("SF Pro Text"), local("SFProText-Regular"), local("Segoe UI"),
      local("Helvetica Neue");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "SF Pro Display";
    src: local("SF Pro Display Medium"), local("SFProDisplay-Medium"),
      local("SF Pro Text Medium"), local("SFProText-Medium"),
      local("Segoe UI Semibold"), local("Helvetica Neue Medium");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "SF Pro Display";
    src: local("SF Pro Display Bold"), local("SFProDisplay-Bold"),
      local("SF Pro Text Bold"), local("SFProText-Bold"), local("Segoe UI Bold"),
      local("Helvetica Neue Bold");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
}

/* Animations and Effects */
@layer components {
  .glass-panel {
    @apply bg-white/80 dark:bg-black/30 backdrop-blur-md border border-white/20 dark:border-white/10 shadow-glass;
  }

  .section-transition {
    @apply transition-all duration-700 ease-out;
  }

  .parallax-slow {
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-medium {
    transition: transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-fast {
    transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-scale {
    @apply transition-transform duration-300 ease-out hover:scale-105;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 3D Chatbot Button Animations */
  .chatbot-siri-pulse {
    animation: siri-pulse 2s ease-in-out infinite;
  }

  .chatbot-breathing {
    animation: breathing 3s ease-in-out infinite;
  }

  .chatbot-glow {
    animation: glow-pulse 2.5s ease-in-out infinite alternate;
  }
}

/* Keyframes for 3D Chatbot Animations */
@keyframes siri-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes breathing {
  0%,
  100% {
    transform: scale(1) translateZ(0);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.02) translateZ(2px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.2), 0 0 60px rgba(59, 130, 246, 0.1);
  }
  100% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5),
      0 0 60px rgba(59, 130, 246, 0.3), 0 0 90px rgba(59, 130, 246, 0.2);
  }

  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  /* Container utilities for better responsive design */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-tight {
    @apply w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  /* Improve button touch targets on mobile */
  button,
  a[role="button"],
  .btn,
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Optimize form inputs for mobile */
  input,
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Fix text alignment issues on mobile */
  .text-justify {
    text-align: left;
  }

  /* Prevent horizontal overflow */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Better line height for readability */
  p,
  div,
  span {
    line-height: 1.6;
  }

  /* Ensure proper spacing between elements */
  .space-y-4 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-6 > * + * {
    margin-top: 1.5rem !important;
  }

  /* Fix grid layouts on mobile */
  .grid {
    gap: 1rem !important;
  }

  /* Ensure flex items don't overflow */
  .flex {
    flex-wrap: wrap;
  }

  /* Better padding for containers */
  .container,
  .max-w-7xl,
  .max-w-6xl,
  .max-w-5xl {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Fix carousel height on mobile */
  .hero-carousel {
    min-height: 400px !important;
    max-height: 60vh !important;
  }

  /* Ensure banner images are properly sized */
  .banner-image {
    object-fit: cover !important;
    object-position: center !important;
    width: 100% !important;
    height: 100% !important;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet-specific styles */
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  /* Desktop-specific styles */
  .desktop-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Optimize for retina displays */
  img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Additional responsive utilities for better mobile experience */
@layer utilities {
  /* Responsive text alignment */
  .text-responsive-center {
    @apply text-center sm:text-left;
  }

  .text-responsive-left {
    @apply text-left;
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }

  /* Responsive spacing utilities */
  .spacing-responsive-sm {
    @apply space-y-2 sm:space-y-3 md:space-y-4;
  }

  .spacing-responsive-md {
    @apply space-y-3 sm:space-y-4 md:space-y-6;
  }

  .spacing-responsive-lg {
    @apply space-y-4 sm:space-y-6 md:space-y-8;
  }

  /* Responsive padding utilities */
  .padding-responsive-sm {
    @apply p-2 sm:p-3 md:p-4;
  }

  .padding-responsive-md {
    @apply p-3 sm:p-4 md:p-6;
  }

  .padding-responsive-lg {
    @apply p-4 sm:p-6 md:p-8;
  }

  /* Responsive margin utilities */
  .margin-responsive-sm {
    @apply m-2 sm:m-3 md:m-4;
  }

  .margin-responsive-md {
    @apply m-3 sm:m-4 md:m-6;
  }

  /* Responsive container utilities */
  .container-responsive {
    @apply w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20;
  }

  /* Responsive grid utilities */
  .grid-responsive-1-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .grid-responsive-1-2-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-1-2-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* Responsive flex utilities */
  .flex-responsive-col {
    @apply flex flex-col sm:flex-row;
  }

  .flex-responsive-row {
    @apply flex flex-row;
  }

  /* Responsive width utilities */
  .width-responsive-full {
    @apply w-full sm:w-auto;
  }

  .width-responsive-auto {
    @apply w-auto;
  }

  /* Anti-overlap utilities */
  .no-overlap {
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .safe-spacing {
    margin: 0.5rem;
    padding: 0.5rem;
  }

  /* Better mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
  }

  /* Responsive image utilities */
  .img-responsive {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: cover;
  }

  .img-responsive-contain {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: contain;
  }

  /* Full container image coverage - no gaps */
  .img-full-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    min-width: 100%;
    min-height: 100%;
  }

  /* Enhanced image utilities for better visual prominence */
  .img-enhanced {
    filter: contrast(1.05) saturate(1.05) brightness(1.02);
    transition: filter 0.3s ease, transform 0.3s ease;
  }

  .img-enhanced:hover {
    filter: contrast(1.1) saturate(1.1) brightness(1.05);
  }

  /* Responsive viewport utilities */
  .min-h-screen-mobile {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .h-screen-mobile {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  /* Better responsive grid utilities */
  .grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  /* Responsive aspect ratio utilities */
  .aspect-video-responsive {
    aspect-ratio: 16/9;
  }

  .aspect-square-responsive {
    aspect-ratio: 1/1;
  }

  .aspect-photo-responsive {
    aspect-ratio: 4/3;
  }

  /* Better mobile scroll behavior */
  .scroll-smooth-mobile {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Responsive text truncation */
  .text-truncate-responsive {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .text-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Radial gradient utility for vignette effects */
  .bg-radial-gradient {
    background: radial-gradient(circle at center, var(--tw-gradient-stops));
  }

  /* Enhanced shadow utilities */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-4xl {
    box-shadow: 0 50px 100px -20px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* Text wrapping utilities */
  .break-words {
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
  }

  /* Prevent text overflow */
  .text-ellipsis-responsive {
    @apply truncate sm:text-clip;
  }
}

.shimmer {
  @apply relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent;
}

/* Footer divider */
.footer-divider {
  @apply w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent;
}
